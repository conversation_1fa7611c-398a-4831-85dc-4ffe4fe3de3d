// Suggestions Handler for CEP RMT
window.CEP_RMT = window.CEP_RMT || {};

window.CEP_RMT.SuggestionsHandler = (function() {
  'use strict';


  const stepCategoryMapping = {
    2: ['Top Stakeholders'],
    4: ['Top Governance Rules'],
    5: ['Top Socio-ecological Challenges'],
    6: ['Top Coordination Mechanisms'],
    8: ['Top Processes'],
    9: [
      'Top Improved Project Resilience Factors',
      'Top Community Satisfaction Factors',
    ],
    10: ['Top Monitoring Indicators'],
    11: ['Top Feedback Mechanisms'],
  };
  
  function populateAutomatedSuggestions(stepNumber) {
    const resultsState = window.CEP_RMT.FormBase.getResultsState();
    
    const categories = stepCategoryMapping[stepNumber];
    if (!categories || categories.length === 0) {
      return; // No suggestions for this step
    }

    const containerId = `step${stepNumber}_suggestions_container`;
    const $container = $(`#${containerId}`);
    
    if ($container.length === 0) {
      return; // Container not found
    }

    // Clear existing suggestions
    $container.empty();

    let suggestionIndex = 1;
    
    // Process each category for this step
    categories.forEach(categoryName => {
      if (resultsState[categoryName] && resultsState[categoryName].recommendations) {
        const recommendations = resultsState[categoryName].recommendations;
        
        recommendations.forEach((recommendation) => {
          if (recommendation.title) {
            const suggestionId = `step${stepNumber}_suggestion_${suggestionIndex}`;
            
            
            const suggestionHtml = `
              <div class="form-check mb-3">
                <input class="form-check-input" type="checkbox" id="${suggestionId}"/>
                <label class="form-check-label checkbox-label" for="${suggestionId}">
                  ${recommendation.title}
                </label>
              </div>
            `;
            
            $container.append(suggestionHtml);
            suggestionIndex++;
          }
        });
      }
    });

    // If no suggestions were added, show a default message
    if (suggestionIndex === 1) {
      $container.html('<p class="text-muted">No automated suggestions available for this step.</p>');
    }

    // Rebind checkbox events for the new suggestions
    bindSuggestionCheckboxEvents(stepNumber);
  }

  // Function to bind checkbox events for suggestions
  function bindSuggestionCheckboxEvents(stepNumber) {
    $(`input[id^="step${stepNumber}_suggestion"]`).off('change').on('change', function () {
      updateSelectedSuggestions(stepNumber);
    });
  }

  // Function to update selected suggestions
  function updateSelectedSuggestions(stepNumber) {
    const formState = window.CEP_RMT.FormBase.getFormState();
    const selectedSuggestions = [];
    
    $(`input[id^="step${stepNumber}_suggestion"]:checked`).each(function () {
      const label = $(this).next('label').text().trim();
      selectedSuggestions.push(label);
    });
    
    // Store in formState based on step
    switch(stepNumber) {
      case 2:
        formState.data.selectedStakeholders = selectedSuggestions;
        break;
      case 4:
        formState.data.selectedGovernance = selectedSuggestions;
        break;
      case 5:
        formState.data.selectedChallenges = selectedSuggestions;
        break;
      case 6:
        formState.data.selectedCoordination = selectedSuggestions;
        break;
      case 8:
        formState.data.selectedImplementation = selectedSuggestions;
        break;
      case 9:
        formState.data.selectedOutcomes = selectedSuggestions;
        break;
      case 10:
        formState.data.selectedMonitoring = selectedSuggestions;
        break;
      case 11:
        formState.data.selectedFeedback = selectedSuggestions;
        break;
    }
  }

  // Function to show references modal
  function showReferencesModal(stepNumber) {
    const resultsState = window.CEP_RMT.FormBase.getResultsState();
    
    const categories = stepCategoryMapping[stepNumber];
    const $modalContent = $('#references-content');
    
    if (!categories || categories.length === 0) {
      $modalContent.html('<p class="text-muted">No references available for this step.</p>');
      $('#referencesModal').modal('show');
      return;
    }

    let referencesHtml = '';
    
    categories.forEach(categoryName => {
      if (resultsState[categoryName] && resultsState[categoryName].references) {
        referencesHtml += `<h6 class="fw-bold text-primary mb-3">${categoryName}</h6>`;
        
        resultsState[categoryName].references.forEach((reference) => {
          referencesHtml += `
            <div class="mb-3 p-3 border rounded">
              <p class="mb-2">"${reference.sentence}"</p>
              <small class="text-muted">Page ${reference.page}, Line ${reference.line}</small>
            </div>
          `;
        });
      }
    });

    if (referencesHtml === '') {
      referencesHtml = '<p class="text-muted">No references available for this step.</p>';
    }

    $modalContent.html(referencesHtml);
    $('#referencesModal').modal('show');
  }

  // Common API submission function for steps 2-11
  async function submitStepData(stepNumber, stepName, inputData) {
    try {
      const formState = window.CEP_RMT.FormBase.getFormState();
      const apiEndpoint = window.CEP_RMT.FormBase.getApiEndpoint();
      
      const payload = {
        step_number: stepNumber,
        step_name: stepName,
        input_data: inputData,
        record_id: formState.recordId
      };

      const response = await fetch(apiEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Requested-With': 'XMLHttpRequest',
        },
        body: JSON.stringify(payload)
      });

      const { result } = await response.json();
      
      if (result.success) {
        window.CEP_RMT.FormBase.showSuccessMessage('Step data saved successfully');
        return result;
      } else {
        throw new Error(result.message || 'Failed to save step data');
      }
    } catch (error) {
      window.CEP_RMT.FormBase.showErrorMessage('Error saving step data: ' + error.message);
      throw error;
    }
  }

  // Public API
  return {
    populateAutomatedSuggestions,
    showReferencesModal,
    submitStepData,
    updateSelectedSuggestions,
    bindSuggestionCheckboxEvents
  };
})();

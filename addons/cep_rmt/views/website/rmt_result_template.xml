<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- Results Template -->
        <template id="recommendation_result_template" name="Recommendation Results">
            <div class="recommendation-results">
                <div class="table-responsive">
                    <table class="table">
                        <thead>
                            <tr>
                                <th class="fw-bold text-center">Section</th>
                                <th class="fw-bold text-center">Details</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- Recommendation Section -->
                            <tr>
                                <td class="fw-bold" style="width: 25%;">Recommendation</td>
                                <td>
                                    <t t-if="result_data.get('recommendation')">
                                        <div class="mb-2">
                                            <span t-esc="result_data.get('recommendation')"></span>
                                        </div>
                                    </t>
                                    <!-- Show recommendation attachments (names only) -->
                                    <t t-if="result_data.get('attachments', {}).get('recommendation_docs')">
                                        <div class="mt-3">
                                            <strong>Recommendation Documents:</strong>
                                            <ul class="list-unstyled mt-2">
                                                <t t-foreach="result_data.get('attachments', {}).get('recommendation_docs', [])" t-as="doc">
                                                    <li class="mb-1">
                                                        <span t-esc="doc.get('name', 'Document')"></span>
                                                    </li>
                                                </t>
                                            </ul>
                                        </div>
                                    </t>
                                    <t t-if="not result_data.get('recommendation') and not result_data.get('attachments', {}).get('recommendation_docs')">
                                        <span class="text-muted">No recommendation data available</span>
                                    </t>
                                </td>
                            </tr>

                            <!-- Stakeholders Section -->
                            <tr>
                                <td class="fw-bold">Stakeholders</td>
                                <td>
                                    <t t-if="result_data.get('stakeholders_involved') != 'No data available'">
                                        <div class="mb-3">
                                            <h6>Stakeholders Involved:</h6>
                                            <span t-esc="result_data.get('stakeholders_involved').get('stakeholders_text')"></span>
                                        </div>
                                    </t>
                                    <t t-if="result_data.get('stakeholders') != 'No data available'">
                                        <div class="mb-2">
                                            <h6>Stakeholder Roles:</h6>
                                            <span t-esc="result_data.get('stakeholders')"></span>
                                        </div>
                                    </t>
                                    <t t-if="result_data.get('stakeholders_involved') == 'No data available' and result_data.get('stakeholders') == 'No data available'">
                                        <span class="text-muted">No stakeholders data available</span>
                                    </t>
                                </td>
                            </tr>

                            <!-- Governance Rules Section -->
                            <tr>
                                <td class="fw-bold">Governance Rules</td>
                                <td>
                                    <span t-esc="result_data.get('governance_rules')"></span>
                                </td>
                            </tr>

                            <!-- Identified Challenges Section -->
                            <tr>
                                <td class="fw-bold">Identified Challenges</td>
                                <td>
                                    <span t-esc="result_data.get('identified_challenges')"></span>
                                </td>
                            </tr>

                            <!-- Coordination Plan Section -->
                            <tr>
                                <td class="fw-bold">Coordination Plan</td>
                                <td>
                                    <span t-esc="result_data.get('coordination_plan')"></span>
                                </td>
                            </tr>

                            <!-- Team Details Section -->
                            <tr>
                                <td class="fw-bold">Team Details</td>
                                <td>
                                    <span t-esc="result_data.get('team_info')"></span>
                                </td>
                            </tr>

                            <!-- Implementation Steps Section -->
                            <tr>
                                <td class="fw-bold">Implementation Steps</td>
                                <td>
                                    <span t-esc="result_data.get('implementation_steps')"></span>
                                </td>
                            </tr>

                            <!-- Expected Outcomes Section -->
                            <tr>
                                <td class="fw-bold">Expected Outcomes</td>
                                <td>
                                    <span t-esc="result_data.get('expected_outcomes')"></span>
                                </td>
                            </tr>

                            <!-- Monitoring Indicators Section -->
                            <tr>
                                <td class="fw-bold">Monitoring Indicators</td>
                                <td>
                                    <span t-esc="result_data.get('monitoring_indicators')"></span>
                                </td>
                            </tr>

                            <!-- Feedback Mechanisms Section -->
                            <tr>
                                <td class="fw-bold">Feedback Mechanisms</td>
                                <td>
                                    <span t-esc="result_data.get('feedback_mechanisms')"></span>
                                </td>
                            </tr>

                            <!-- Supporting Docs Section -->
                            <tr>
                                <td class="fw-bold">Supporting Docs</td>
                                <td>
                                    <!-- Display supporting documents data from JSON -->
                                    <t t-if="result_data.get('supporting_docs') != 'No data available'">
                                        <div class="mb-3">
                                            <span t-esc="result_data.get('supporting_docs')"></span>
                                        </div>
                                    </t>

                                    <!-- Display supporting document attachments as clickable links -->
                                    <t t-if="result_data.get('attachments', {}).get('supporting_docs')">
                                        <div class="mt-3">
                                            <strong>Supporting Document Attachments:</strong>
                                            <ul class="list-unstyled mt-2">
                                                <t t-foreach="result_data.get('attachments', {}).get('supporting_docs', [])" t-as="doc">
                                                    <li class="mb-2">
                                                        <a t-att-href="doc.get('url', '#')" target="_blank" class="text-primary text-decoration-none">
                                                            <i class="fa fa-download me-2"></i>
                                                            <span t-esc="doc.get('name', 'Document')"></span>
                                                        </a>
                                                        <t t-if="doc.get('file_size')">
                                                            <small class="text-muted ms-2">
                                                                (<span t-esc="'%.1f KB' % (doc.get('file_size', 0) / 1024)"></span>)
                                                            </small>
                                                        </t>
                                                    </li>
                                                </t>
                                            </ul>
                                        </div>
                                    </t>

                                    <t t-if="result_data.get('supporting_docs') == 'No data available' and not result_data.get('attachments', {}).get('supporting_docs')">
                                        <span class="text-muted">No supporting documents available</span>
                                    </t>
                                </td>
                            </tr>

                        </tbody>
                    </table>
                </div>
            </div>
        </template>

    </data>
</odoo>